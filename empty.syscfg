/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const PWM2    = PWM.addInstance();
const PWM3    = PWM.addInstance();
const PWM4    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                          = "Snsor";
GPIO1.associatedPins.create(4);
GPIO1.associatedPins[0].$name        = "X1";
GPIO1.associatedPins[0].direction    = "INPUT";
GPIO1.associatedPins[0].assignedPort = "PORTA";
GPIO1.associatedPins[0].pin.$assign  = "PA24";
GPIO1.associatedPins[1].$name        = "X2";
GPIO1.associatedPins[1].direction    = "INPUT";
GPIO1.associatedPins[1].assignedPort = "PORTA";
GPIO1.associatedPins[1].pin.$assign  = "PA25";
GPIO1.associatedPins[2].$name        = "X3";
GPIO1.associatedPins[2].direction    = "INPUT";
GPIO1.associatedPins[2].assignedPort = "PORTA";
GPIO1.associatedPins[2].pin.$assign  = "PA26";
GPIO1.associatedPins[3].$name        = "X4";
GPIO1.associatedPins[3].direction    = "INPUT";
GPIO1.associatedPins[3].assignedPort = "PORTA";
GPIO1.associatedPins[3].pin.$assign  = "PA27";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

PWM1.clockDivider                       = 8;
PWM1.$name                              = "PWM_L1";
PWM1.clockPrescale                      = 40;
PWM1.peripheral.ccp0Pin.$assign         = "PA12";
PWM1.peripheral.ccp1Pin.$assign         = "PA13";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";

PWM2.clockDivider                       = 8;
PWM2.$name                              = "PWM_R1";
PWM2.clockPrescale                      = 40;
PWM2.peripheral.$assign                 = "TIMG6";
PWM2.peripheral.ccp0Pin.$assign         = "PA5";
PWM2.peripheral.ccp1Pin.$assign         = "PA6";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");

PWM3.$name                              = "PWM_L2";
PWM3.clockDivider                       = 8;
PWM3.clockPrescale                      = 40;
PWM3.peripheral.ccp0Pin.$assign         = "PA21";
PWM3.peripheral.ccp1Pin.$assign         = "PA22";
PWM3.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC4";
PWM3.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC5";
PWM3.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
PWM3.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM3.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM3.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM3.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM3.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
PWM3.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM3.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM3.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM3.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");

PWM4.$name                              = "PWM_R2";
PWM4.clockDivider                       = 8;
PWM4.clockPrescale                      = 40;
PWM4.peripheral.ccp0Pin.$assign         = "PA1";
PWM4.peripheral.ccp1Pin.$assign         = "PA2";
PWM4.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC8";
PWM4.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC9";
PWM4.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM4.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM4.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM4.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM4.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric8";
PWM4.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM4.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM4.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM4.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM4.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric9";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable  = true;
SYSTICK.period        = 32;
SYSTICK.systickEnable = true;

UART1.$name                    = "UART_0";
UART1.peripheral.rxPin.$assign = "PA9";
UART1.peripheral.txPin.$assign = "PA8";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric6";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric7";

UART2.$name                    = "UART_1";
UART2.peripheral.rxPin.$assign = "PA11";
UART2.peripheral.txPin.$assign = "PA10";
UART2.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric10";
UART2.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric11";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution          = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution = "PA20";
Board.peripheral.swdioPin.$suggestSolution = "PA19";
PWM1.peripheral.$suggestSolution           = "TIMG0";
PWM3.peripheral.$suggestSolution           = "TIMA0";
PWM4.peripheral.$suggestSolution           = "TIMG8";
UART1.peripheral.$suggestSolution          = "UART1";
UART2.peripheral.$suggestSolution          = "UART0";
