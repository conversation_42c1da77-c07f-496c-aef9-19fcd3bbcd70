#include "ti_msp_dl_config.h"
#include "bsp_at8236.h"

//自定义延时函数，不够精确
void delay_ms(unsigned int ms) 
{
    unsigned int i, j;
    // 这里的嵌套循环的次数是根据处理器频率和编译器生成的指令周期大致计算出来的，
    // 需要通过实际测试调整来达到准确的延时
    for (i = 0; i < ms; i++) 
    {
        for (j = 0; j < 8000; j++) 
        {
            // 这执行一个足够简单的操作来控制预期的执行时间的操作
            __asm__("nop"); // "nop" 是无操作指令，用于在代码中能够有效，消耗掉一个或几个时钟周期
        }
    }
}     




//主函数
int main(void)
{
    SYSCFG_DL_init();
	
		init_motor();//初始化电机

    while (1) 
    {    
			L1_control(600,0);//0-1000 1:正转
			L2_control(700,0);//0-1000 0:反转
			
			R1_control(400,1);//0-1000 1:正转
			R2_control(300,1);//0-1000 0:反转
       
    }
} 



