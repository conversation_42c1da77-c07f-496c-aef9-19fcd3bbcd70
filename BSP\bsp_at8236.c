#include "bsp_at8236.h"

void init_motor(void)
{
	DL_TimerA_startCounter(PWM_L1_INST);
	DL_TimerA_startCounter(PWM_L2_INST);
	DL_TimerA_startCounter(PWM_R1_INST);
	DL_TimerA_startCounter(PWM_R2_INST);
}


void L1_control(uint16_t motor_speed,uint8_t dir)
{
	if(dir)
	{
			DL_TimerA_setCaptureCompareValue(PWM_L1_INST, motor_speed, DL_TIMER_CC_0_INDEX);
			DL_TimerA_setCaptureCompareValue(PWM_L1_INST, 0, DL_TIMER_CC_1_INDEX);
	}
	else
	{
			DL_TimerA_setCaptureCompareValue(PWM_L1_INST, 0, DL_TIMER_CC_0_INDEX);
			DL_TimerA_setCaptureCompareValue(PWM_L1_INST, motor_speed, DL_TIMER_CC_1_INDEX);
	}

}



void L2_control(uint16_t motor_speed,uint8_t dir)
{
	if(dir)
	{
			DL_TimerA_setCaptureCompareValue(PWM_L2_INST, motor_speed, DL_TIMER_CC_0_INDEX);
			DL_TimerA_setCaptureCompareValue(PWM_L2_INST, 0, DL_TIMER_CC_1_INDEX);
	}
	else
	{
			DL_TimerA_setCaptureCompareValue(PWM_L2_INST, 0, DL_TIMER_CC_0_INDEX);
			DL_TimerA_setCaptureCompareValue(PWM_L2_INST, motor_speed, DL_TIMER_CC_1_INDEX);
	}
		
}

void R1_control(uint16_t motor_speed,uint8_t dir)
{
	if(dir)
	{
			DL_TimerA_setCaptureCompareValue(PWM_R1_INST, motor_speed, DL_TIMER_CC_0_INDEX);
			DL_TimerA_setCaptureCompareValue(PWM_R1_INST, 0, DL_TIMER_CC_1_INDEX);
			
	}
	else
	{
			DL_TimerA_setCaptureCompareValue(PWM_R1_INST, 0, DL_TIMER_CC_0_INDEX);
			DL_TimerA_setCaptureCompareValue(PWM_R1_INST, motor_speed, DL_TIMER_CC_1_INDEX);
	}
		
}


void R2_control(uint16_t motor_speed,uint8_t dir)
{
	if(dir)
	{
			DL_TimerA_setCaptureCompareValue(PWM_R2_INST, 0, DL_TIMER_CC_0_INDEX);
			DL_TimerA_setCaptureCompareValue(PWM_R2_INST, motor_speed, DL_TIMER_CC_1_INDEX);
	}
	else
	{
			DL_TimerA_setCaptureCompareValue(PWM_R2_INST, motor_speed, DL_TIMER_CC_0_INDEX);
			DL_TimerA_setCaptureCompareValue(PWM_R2_INST, 0, DL_TIMER_CC_1_INDEX);
	}
		
}