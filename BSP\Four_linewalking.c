#include "Four_linewalking.h"
#include "usart.h"

#define IRTrack_Trun_KP (450)
#define IRTrack_Trun_KI (0) 
#define IRTrack_Trun_KD (0) 

int pid_output_IRR = 0;
float err = 0;

int IRR_SPEED = 300;//��ʼֱ���ٶ�    Initial linear velocity

int Left_rui = 0;
int Right_rui = 0;
int turn = 0;

float APP_IR_PID_Calc(float actual_value)
{

	float IRTrackTurn = 0;
	int8_t error;
	static int8_t error_last=0;
	static float IRTrack_Integral;//����  Integral
	

	error=actual_value;
	
	IRTrack_Integral +=error;
	
	//λ��ʽpid    Positional pid
	IRTrackTurn=error*IRTrack_Trun_KP
							+IRTrack_Trun_KI*IRTrack_Integral
							+(error - error_last)*IRTrack_Trun_KD;
	return IRTrackTurn;
}

//��ȡX1X2X3X4�����ŵ�ƽ	Get the pin levels of X1X2X3X4
void Four_GetLineWalking(int *LineL1, int *LineL2, int *LineR1, int *LineR2)
{
	*LineL1 = LineWalk_L1_IN;
	*LineL2 = LineWalk_L2_IN;
	*LineR1 = LineWalk_R1_IN;
	*LineR2 = LineWalk_R2_IN;
}

void Four_LineWalking(void)
{
	int LineL1 = 0, LineL2 = 0, LineR1 = 0, LineR2 = 0;
	Four_GetLineWalking(&LineL1, &LineL2, &LineR1, &LineR2);//��ȡ���߼���״̬	Get black line detection status
    //debug
//    printf("L1:%d L2:%d R1:%d R2:%d\r\n",LineL1, LineL2, LineR1, LineR2);

    // 0 0 X 0
    // 1 0 X 0
    // 0 1 X 0
    //���������Ǻ���ֱ�ǵ�ת��
    //Processing the right acute angle and the right right angle rotation
	if( (LineL1 == LOW || LineL2 == LOW) && LineR2 == LOW) 
    {
        err=13;
		delay_ms(80);
    }
   // 0 X 0 0       
   // 0 X 0 1 
   // 0 X 1 0       
   //���������Ǻ���ֱ�ǵ�ת��
    //Handling left acute angle and left right angle rotation
    else if ( LineL1 == LOW && (LineR1 == LOW || LineR2 == LOW)) 
	{ 
        err=-13;
		delay_ms(80);
    }
    // 0 X X X
   //�����߼��⵽
    //Most left detected
    else if( LineL1 == LOW )
    {  
        err=-9;
		delay_ms(10);
	}
    // X X X 0
   //���ұ߼��⵽
    //Most right detected
    else if ( LineR2 == LOW)
    {  
        err=9;
//		Contrl_Speed(500,500,-500,-500);
		delay_ms(10);
	}
    // X 0 1 X
   //������С��
    //Processing of the left hand chicane
    else if (LineL2 == LOW && LineR1 == HIGH) //�м������ϵĴ�����΢������ת  Sensor on the black line in the center fine tunes the car to turn left
    {   
		err=-1;
	}
    // X 1 0 X  
   //������С��
    //Processing of the right-hand chicane
	else if (LineL2 == HIGH && LineR1 == LOW) //�м������ϵĴ�����΢������ת  The sensor on the center black line fine tunes the car to turn right
    {   
		err=1;
	}
    // X 0 0 X
   //����ֱ��
    //Processing straight lines
    else if(LineL2 == LOW && LineR1 == LOW) // ���Ǻ�ɫ, ����ǰ��   It's all black, so speed up.
    {  
        err=0;
	}	
    // 0 0 0 0
    else if(LineL1 == LOW && LineL2 == LOW && LineR1 == LOW && LineR2 == LOW) // ���Ǻ�ɫ, ����ǰ�� It's all black, so speed up.
    {  
		err = 0;
	}
    
    //��Ϊ1 1 1 1ʱС��������һ��С������״̬ 
    //When it is 1 1 1 1 the trolley keeps the previous trolley in operation 
    
    pid_output_IRR = (int)(APP_IR_PID_Calc(err));
	
	Motion_Car_Control(IRR_SPEED, 0, pid_output_IRR);
    
}

